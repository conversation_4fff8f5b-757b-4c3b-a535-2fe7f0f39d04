import request, { returnData } from '@/services';
import {
  Dataset,
  GetDatasetListParams,
  GetDatasetDetailParams,
  DatasetDetailItem,
  DeleteDatasetParams,
  UploadDatasetResponse,
} from './model';

export const getDatasetList = (
  params: GetDatasetListParams,
): Promise<returnData<Dataset[]>> => {
  return request.get('/api/dataset/getDatasetList', { params });
};

export const getDatasetDetail = (
  params: GetDatasetDetailParams,
): Promise<returnData<DatasetDetailItem[]>> => {
  return request.get('/api/dataset/getDatasetDetailList', { params });
};

export const deleteDataset = (
  params: DeleteDatasetParams,
): Promise<returnData<null>> => {
  return request.post('/api/dataset/deleteDataset', {
    datasetUuid: params.datasetUuid,
  });
};

export const uploadDataset = (
  params: FormData,
): Promise<returnData<UploadDatasetResponse>> => {
  return request.post('/api/dataset/uploadDataset', params, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const getDatasetBaseInfoByUuid = (datasetUuid: string) => {
  return request.get('/api/dataset/getDatasetBaseInfoByUuid', {
    params: {
      datasetUuid,
    },
  });
};

export const getDatasetLog = (params: { datasetUuid: string }) => {
  return request.get('/api/scpLog/getDatasetLogs', { params });
};

export const getStatusForDatasets = () => {
  return request.get('/api/dataset/getDatasetStateList');
};

export const confirmJsonlSelection = (params: {
  datasetUuid: string;
  selectedJsonl: string;
}): Promise<returnData<null>> => {
  return request.post('/api/dataset/confirmJsonlSelection', params);
};

export const uploadFolderForDataSet = (
  params: FormData,
): Promise<returnData<UploadDatasetResponse>> => {
  return request.post('/api/file/uploadFolderForDataSet', params, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

const test_json = {
  image_id: 'image1',
  annotationGroups: [
    {
      label: 'items',
      annotations: [
        {
          id: 'object_001',
          label: 'airConditioner',
          bboxes: [[60, 65, 100, 80]],
        },
        {
          id: 'object_002',
          label: 'desk',
          bboxes: [[60, 65, 100, 80]],
          segmentation: [[100, 100, 150, 200, 50, 200]],
          attributes: {
            material: 'wood',
          },
        },
      ],
    },
    {
      label: 'people',
      annotations: [
        {
          id: 'object_003',
          label: 'man',
          bboxes: [[60, 65, 100, 80]],
          attributes: {
            pose: 'standing',
          },
        },
        {
          id: 'object_004',
          label: 'woman',
          segmentation: [[100, 100, 150, 200, 50, 200]],
        },
      ],
    },
    {
      label: 'action',
      annotations: [
        {
          id: 'object_005',
          label: 'man',
          bboxes: [[60, 65, 100, 80]],
          attributes: {
            pose: 'standing',
          },
        },
        {
          id: 'object_006',
          label: 'mouth',
          segmentation: [[100, 100, 150, 200, 50, 200]],
        },
        {
          id: 'object_007',
          label: 'coffee',
          bboxes: [[60, 65, 100, 80]],
        },
      ],
    },
  ],
  qa: [
    {
      question: '图片1描述了什么？',
      answer: '在一个有空调和桌子的房间内，有男人和女人，其中一个男人在喝咖啡',
      lang: 'zh',
      type: 'caption',
    },
  ],
};

export const testApi = () => {
  return new Promise((resolve, reject) => {
    resolve({
      code: 0,
      data: {
        result: test_json,
      },
    });
  });
};
