import React, { useEffect, useState, useMemo } from 'react';
import { Table, Image, Button, Card } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail, testApi } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({ datasetUuid, changTotalData }) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);
  const [testApiData, setTestApiData] = useState<any>(null);
  const [showTestData, setShowTestData] = useState(false);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({ datasetUuid, page, pageSize });
      if (res.code === 0) {
        const list = Array.isArray(res.data) ? res.data : res.data.list;
        setDataList(list || []); // 只保存列表数据，不再设置详情

        if (res.data.page) {
          setPagination({
            current: res.data.page,
            pageSize: res.data.pageSize || 10,
            total: res.data.total || 0,
          });
          changTotalData(res.data.total || 0);
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTestApiData = async () => {
    try {
      const res: any = await testApi();
      if (res.code === 0) {
        setTestApiData(res.data.result);
        setShowTestData(true);
      }
    } catch (error) {
      console.error('获取测试数据失败：', error);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (record: DatasetDetailItem) => {
    setExpandedRowKeys((prev) =>
      prev.includes(String(record.id))
        ? prev.filter((key) => key !== String(record.id))
        : [...prev, String(record.id)],
    );
  };
  const columns: ColumnsType<DatasetDetailItem> = useMemo(() => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());
          // 判断是否为图片列
          const isImageCol =
            key.toLowerCase().includes('image') ||
            key.toLowerCase().includes('img') ||
            key.toLowerCase().includes('pic') ||
            key.toLowerCase().includes('url');
          // 支持无扩展名的图片链接（如 unsplash）
          const isImgUrl =
            typeof value === 'string' && /^https?:\/\//.test(value);
          if (isImageCol && isImgUrl) {
            return (
              <Image
                src={value}
                alt='图片'
                style={{
                  maxWidth: 120,
                  maxHeight: 80,
                  objectFit: 'contain',
                  borderRadius: 4,
                }}
                preview={true}
                placeholder={
                  <div
                    style={{ width: 120, height: 80, background: '#f0f0f0' }}
                  />
                }
              />
            );
          }
          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`$${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`$${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  }, [dataList, expandedRowKeys]);

  useEffect(() => {
    fetchDetail();
  }, []);

  // 判断数据集类型：纯文字 vs 视觉数据集
  const getDatasetType = (data: any) => {
    if (!data) return 'text';
    // 如果有图片ID或标注组，认为是视觉数据集
    if (data.image_id || data.annotationGroups) return 'visual';
    return 'text';
  };

  // 将testApi数据转换为表格数据
  const convertTestApiToTableData = () => {
    if (!testApiData) return [];

    const datasetType = getDatasetType(testApiData);
    const tableData: any[] = [];

    if (datasetType === 'visual') {
      // 视觉数据集：为每个标注创建一行
      testApiData.annotationGroups?.forEach(
        (group: any, groupIndex: number) => {
          group.annotations?.forEach((annotation: any, annIndex: number) => {
            tableData.push({
              key: `${groupIndex}-${annIndex}`,
              id: annotation.id,
              image_id: testApiData.image_id,
              group_label: group.label,
              annotation_label: annotation.label,
              bboxes: annotation.bboxes
                ? JSON.stringify(annotation.bboxes)
                : '',
              segmentation: annotation.segmentation
                ? JSON.stringify(annotation.segmentation)
                : '',
              attributes: annotation.attributes
                ? JSON.stringify(annotation.attributes)
                : '',
              type: 'visual',
            });
          });
        },
      );

      // 添加问答数据
      testApiData.qa?.forEach((qa: any, qaIndex: number) => {
        tableData.push({
          key: `qa-${qaIndex}`,
          id: `qa_${qaIndex}`,
          image_id: testApiData.image_id,
          question: qa.question,
          answer: qa.answer,
          language: qa.lang,
          qa_type: qa.type,
          type: 'qa',
        });
      });
    } else {
      // 纯文字数据集处理
      tableData.push({
        key: 'text-data',
        content: JSON.stringify(testApiData),
        type: 'text',
      });
    }

    return tableData;
  };

  // 获取表格列配置
  const getTestApiTableColumns = (): ColumnsType<any> => {
    if (!testApiData) return [];

    const datasetType = getDatasetType(testApiData);

    if (datasetType === 'visual') {
      return [
        {
          title: '序号',
          key: 'index',
          width: 80,
          align: 'center',
          render: (_: any, __: any, index: number) => index + 1,
        },
        {
          title: '图片',
          key: 'image',
          width: 120,
          render: (_, record) => (
            <Image
              src='/api/placeholder/100/80' // 默认占位图片
              alt='数据集图片'
              style={{
                width: 100,
                height: 80,
                objectFit: 'cover',
                borderRadius: 4,
              }}
              preview={true}
              placeholder={
                <div
                  style={{
                    width: 100,
                    height: 80,
                    background: '#f0f0f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 4,
                  }}>
                  图片
                </div>
              }
            />
          ),
        },
        {
          title: '图片ID',
          dataIndex: 'image_id',
          key: 'image_id',
          width: 120,
        },
        {
          title: '类型',
          dataIndex: 'type',
          key: 'type',
          width: 80,
          render: (type: string) => (
            <span
              className={
                type === 'visual' ? 'text-blue-600' : 'text-green-600'
              }>
              {type === 'visual' ? '标注' : '问答'}
            </span>
          ),
        },
        {
          title: '组标签',
          dataIndex: 'group_label',
          key: 'group_label',
          width: 100,
        },
        {
          title: '标注标签',
          dataIndex: 'annotation_label',
          key: 'annotation_label',
          width: 120,
        },
        {
          title: '问题',
          dataIndex: 'question',
          key: 'question',
          width: 200,
          ellipsis: true,
        },
        {
          title: '答案',
          dataIndex: 'answer',
          key: 'answer',
          width: 300,
          ellipsis: true,
        },
        {
          title: '边界框',
          dataIndex: 'bboxes',
          key: 'bboxes',
          width: 150,
          ellipsis: true,
        },
        {
          title: '分割',
          dataIndex: 'segmentation',
          key: 'segmentation',
          width: 150,
          ellipsis: true,
        },
        {
          title: '属性',
          dataIndex: 'attributes',
          key: 'attributes',
          width: 150,
          ellipsis: true,
        },
      ];
    } else {
      // 纯文字数据集列配置
      return [
        {
          title: '序号',
          key: 'index',
          width: 80,
          align: 'center',
          render: (_: any, __: any, index: number) => index + 1,
        },
        {
          title: '内容',
          dataIndex: 'content',
          key: 'content',
          ellipsis: true,
        },
      ];
    }
  };

  const renderTestApiData = () => {
    if (!testApiData) return null;

    const tableData = convertTestApiToTableData();
    const columns = getTestApiTableColumns();
    const datasetType = getDatasetType(testApiData);

    return (
      <Card
        title={`测试API数据 - ${datasetType === 'visual' ? '视觉数据集' : '纯文字数据集'}`}
        className='mb-4'>
        <Table
          bordered
          size='small'
          columns={columns}
          dataSource={tableData}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条数据`,
          }}
          scroll={{ x: 'max-content' }}
        />
      </Card>
    );
  };

  return (
    <div>
      <div className='mb-4 flex gap-2'>
        <Button type='primary' onClick={fetchTestApiData}>
          加载测试API数据
        </Button>
        {showTestData && (
          <Button onClick={() => setShowTestData(!showTestData)}>
            {showTestData ? '隐藏' : '显示'}测试数据
          </Button>
        )}
      </div>

      {showTestData && renderTestApiData()}

      <Table
        bordered
        rowKey='id'
        columns={columns}
        dataSource={dataList}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          style: { cursor: 'pointer' },
        })}
        scroll={{ x: 'max-content' }}
      />
    </div>
  );
};

export default DataPart;
